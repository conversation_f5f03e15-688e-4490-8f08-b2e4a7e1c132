#!/usr/bin/env python3
"""
DataProvider - Simple data provider for live trading initialization.
Primarily used for historical data loading during startup.
"""

import logging
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Dict, Any, Optional
import pandas as pd
import pyarrow.parquet as pq

log = logging.getLogger("DataProvider")

def safe_read_parquet(file_path, **kwargs):
    """
    Safe wrapper around pd.read_parquet that validates data quality.
    Detects and handles corrupted market data with 0.00000 prices.
    """
    try:
        df = pd.read_parquet(file_path, **kwargs)
        
        # Check if this is market OHLC data
        ohlc_cols = ['open', 'high', 'low', 'close']
        available_ohlc = [col for col in ohlc_cols if col in df.columns]
        
        if available_ohlc:
            # Check for corrupted 0.00000 prices in OHLC data
            for col in available_ohlc:
                zero_mask = (df[col] == 0.0) | (df[col].isna())
                if zero_mask.any():
                    corrupted_count = zero_mask.sum()
                    total_rows = len(df)
                    
                    if corrupted_count > 0:
                        log.warning(f"⚠️ DATA CORRUPTION DETECTED in {file_path}")
                        log.warning(f"   Column '{col}' has {corrupted_count}/{total_rows} corrupted (0.00000) values")
                        
                        # Show first few corrupted timestamps for debugging
                        if 'timestamp' in df.columns:
                            corrupted_rows = df[zero_mask]
                            for idx, row in corrupted_rows.head(3).iterrows():
                                timestamp = pd.to_datetime(row['timestamp'], utc=True) if pd.notna(row['timestamp']) else 'Unknown'
                                ohlc_vals = {c: row.get(c, 'N/A') for c in available_ohlc}
                                log.warning(f"   {timestamp}: {ohlc_vals}")
                        
                        # CRITICAL: Remove corrupted rows to prevent trading losses
                        # Only remove rows where ANY OHLC value is 0.0 or NaN
                        corruption_mask = pd.Series(False, index=df.index)
                        for ohlc_col in available_ohlc:
                            corruption_mask |= (df[ohlc_col] == 0.0) | (df[ohlc_col].isna())
                        
                        if corruption_mask.any():
                            clean_df = df[~corruption_mask].copy()
                            removed_count = len(df) - len(clean_df)
                            log.warning(f"   🧹 CLEANED: Removed {removed_count} corrupted rows")
                            log.warning(f"   ✅ SAFE DATA: {len(clean_df)} clean rows remaining")
                            df = clean_df
                        
                        # If too much data is corrupted, raise error
                        if len(df) < total_rows * 0.5:  # More than 50% corrupted
                            raise ValueError(f"File {file_path} has too much corrupted data ({corrupted_count}/{total_rows} rows). Cannot proceed safely.")
        
        return df
        
    except Exception as e:
        log.error(f"Error reading {file_path}: {e}")
        raise

class DataProvider:
    """
    Data provider for live trading system.
    Handles historical data loading for agent initialization and warm-up.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize DataProvider with configuration."""
        self.config = config
        self.symbol = config.get('dataProvider', {}).get('symbol', 'XRPUSDC')
        self.timeframe = config.get('primaryTimeframe', '5m')
        
        # Data directory setup
        data_dir_config = config.get('featureParquetDir', 'parquet')
        self.data_dir = Path(data_dir_config).expanduser()
        
        log.info(f"DataProvider initialized for {self.symbol}/{self.timeframe}")
        log.info(f"Data directory: {self.data_dir}")
    
    def load_historical_data(self, start_date: datetime, end_date: datetime,
                           lookback_hours: int = 24) -> Optional[pd.DataFrame]:
        """
        Load historical data for agent warm-up.

        Args:
            start_date: Start date for data loading
            end_date: End date for data loading
            lookback_hours: Additional hours to load before start_date for indicators

        Returns:
            DataFrame with historical data or None if failed
        """
        try:
            # Extend start date for indicator warm-up
            warm_start = start_date - timedelta(hours=lookback_hours)

            # Construct data path
            symbol_tf_dir = self.data_dir / self.symbol / self.timeframe
            if not symbol_tf_dir.exists():
                log.warning(f"Data directory not found: {symbol_tf_dir}")
                return None

            # CRITICAL FIX: Find available data files instead of only looking for requested dates
            available_files = list(symbol_tf_dir.glob("*.parquet"))
            if not available_files:
                log.error(f"No parquet files found in {symbol_tf_dir}")
                return None

            # Sort files by date to get most recent data
            available_files.sort()
            log.info(f"Found {len(available_files)} available data files in {symbol_tf_dir}")

            # Load daily parquet files - try requested range first, then fallback to available data
            dfs = []
            current_date = warm_start.date()
            files_loaded = 0

            # First pass: try to load requested date range
            while current_date <= end_date.date() and files_loaded < 10:  # Limit to prevent excessive loading
                day_file = symbol_tf_dir / f"{current_date}.parquet"
                if day_file.exists():
                    try:
                        df = safe_read_parquet(day_file, engine="pyarrow")
                        if "timestamp" in df.columns:
                            df.set_index("timestamp", inplace=True)
                        df.index = pd.to_datetime(df.index, utc=True)
                        dfs.append(df)
                        files_loaded += 1
                        log.debug(f"Loaded {len(df)} rows from {day_file}")
                    except Exception as e:
                        log.warning(f"Error loading {day_file}: {e}")
                else:
                    if current_date >= start_date.date():
                        log.debug(f"Missing data file: {day_file}")

                current_date += timedelta(days=1)

            # FALLBACK: If no files found in requested range, load most recent available files
            if not dfs and available_files:
                log.warning(f"No data found in requested range {start_date.date()} to {end_date.date()}")
                log.info("🔄 FALLBACK: Loading most recent available historical data for indicators...")

                # Load the most recent files (up to 5 files for indicator calculation)
                recent_files = available_files[-5:]  # Take last 5 files
                for day_file in recent_files:
                    try:
                        df = safe_read_parquet(day_file, engine="pyarrow")
                        if "timestamp" in df.columns:
                            df.set_index("timestamp", inplace=True)
                        df.index = pd.to_datetime(df.index, utc=True)
                        dfs.append(df)
                        files_loaded += 1
                        log.info(f"✅ FALLBACK: Loaded {len(df)} rows from {day_file}")
                    except Exception as e:
                        log.warning(f"Error loading fallback file {day_file}: {e}")

            if not dfs:
                log.error("No historical data files could be loaded")
                return None

            # Combine all data
            combined_df = pd.concat(dfs).sort_index()

            # For fallback data, don't filter by time range - use all available data
            if files_loaded > 0:
                log.info(f"✅ Loaded {len(combined_df)} historical records from {files_loaded} files")
                log.info(f"   Data range: {combined_df.index[0]} to {combined_df.index[-1]}")
                return combined_df
            else:
                return None

        except Exception as e:
            log.error(f"Error loading historical data: {e}")
            return None
    
    def get_latest_price(self, symbol: Optional[str] = None) -> Optional[float]:
        """
        Get latest price for symbol (placeholder for live implementation).
        In live trading, this would query an exchange API.
        """
        try:
            # For now, try to get from most recent historical data
            if symbol is None:
                symbol = self.symbol
            
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(hours=1)
            
            recent_data = self.load_historical_data(start_time, end_time, lookback_hours=0)
            if recent_data is not None and not recent_data.empty:
                latest_price = recent_data['close'].iloc[-1]
                log.debug(f"Latest price for {symbol}: {latest_price}")
                return float(latest_price)
            
        except Exception as e:
            log.warning(f"Error getting latest price for {symbol}: {e}")
        
        return None
    
    def validate_data_availability(self, start_date: datetime, end_date: datetime) -> bool:
        """
        Check if data is available for the requested time range.
        
        Returns:
            True if sufficient data is available, False otherwise
        """
        try:
            symbol_tf_dir = self.data_dir / self.symbol / self.timeframe
            if not symbol_tf_dir.exists():
                log.error(f"Data directory not found: {symbol_tf_dir}")
                return False
            
            # Check if we have at least some files in the date range
            current_date = start_date.date()
            found_files = 0
            total_days = (end_date.date() - start_date.date()).days + 1
            
            while current_date <= end_date.date():
                day_file = symbol_tf_dir / f"{current_date}.parquet"
                if day_file.exists():
                    found_files += 1
                current_date += timedelta(days=1)
            
            coverage_ratio = found_files / total_days if total_days > 0 else 0
            log.info(f"Data coverage: {found_files}/{total_days} days ({coverage_ratio:.1%})")
            
            # Require at least 80% coverage
            return coverage_ratio >= 0.8
            
        except Exception as e:
            log.error(f"Error validating data availability: {e}")
            return False
    
    def get_symbol_info(self) -> Dict[str, Any]:
        """
        Get symbol information for trading.
        """
        return {
            'symbol': self.symbol,
            'timeframe': self.timeframe,
            'data_dir': str(self.data_dir),
            'base_asset': self.symbol[:-4] if len(self.symbol) > 4 else self.symbol[:3],
            'quote_asset': self.symbol[-4:] if len(self.symbol) > 4 else self.symbol[3:]
        }